import {
  Controller,
  Post,
  Get,
  Inject,
  Body,
  Param,
  Query,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { ResponseService } from '../service/response.service';
import { SubmitResponseDTO, QueryResponseDTO } from '../dto/response.dto';
import { BaseController } from '../common/BaseController';
import { ApiResponseUtil } from '../common/ApiResponse';
import { ParamError } from '../error/custom.error';

@Controller('/api/response')
export class ResponseController extends BaseController {
  @Inject()
  responseService: ResponseService;

  /**
   * 提交问卷响应
   */
  @Post('/')
  @Validate()
  async submitResponse(@Body() submitDto: SubmitResponseDTO) {
    // 记录提交IP
    const clientIP =
      this.ctx.request.ip ||
      this.ctx.request.headers['x-forwarded-for'] ||
      this.ctx.request.headers['x-real-ip'] ||
      'unknown';

    this.ctx.logger.info('问卷响应提交请求', {
      questionnaire_id: submitDto.questionnaire_id,
      parent_phone: submitDto.parent_phone,
      student_code: submitDto.sso_student_code,
      month: submitDto.month,
      teacher_count: submitDto.teacher_evaluations.length,
      client_ip: clientIP,
    });

    const response = await this.responseService.submitResponse(submitDto);

    this.ctx.logger.info('问卷响应提交成功', {
      response_id: response.id,
      questionnaire_id: submitDto.questionnaire_id,
      parent_phone: submitDto.parent_phone,
    });

    const responseData = {
      response_id: response.id,
      questionnaire_id: response.questionnaire_id,
      submission_time: response.created_at,
      total_average_score: response.total_average_score,
      teacher_count: response.teacher_count,
    };

    return ApiResponseUtil.success(responseData, '问卷提交成功');
  }

  /**
   * 获取响应列表
   */
  @Get('/')
  @Validate()
  async getResponseList(@Query() queryDto: QueryResponseDTO) {
    const result = await this.responseService.getResponseList(queryDto);
    return ApiResponseUtil.success(result, '获取响应列表成功');
  }

  /**
   * 根据ID获取响应详情
   */
  @Get('/:id')
  async getResponseById(@Param('id') id: number) {
    const response = await this.responseService.getResponseById(id);
    return ApiResponseUtil.success(response, '获取响应详情成功');
  }

  /**
   * 检查是否已提交响应
   */
  @Get('/check')
  async checkResponseExists(
    @Query()
    query: {
      parent_phone: string;
      questionnaire_id: number;
      sso_student_code: string;
      month: string;
    }
  ) {
    const { parent_phone, questionnaire_id, sso_student_code, month } = query;

    if (!parent_phone || !questionnaire_id || !sso_student_code || !month) {
      throw new ParamError('缺少必要参数');
    }

    const exists = await this.responseService.checkResponseExists(
      parent_phone,
      questionnaire_id,
      sso_student_code,
      month
    );

    const responseData = {
      exists,
      message: exists ? '该家长已为此学生在本月提交过问卷' : '可以提交问卷',
    };

    return ApiResponseUtil.success(responseData, '检查完成');
  }

  /**
   * 获取问卷统计信息
   */
  @Get('/statistics/:questionnaireId')
  async getQuestionnaireStatistics(
    @Param('questionnaireId') questionnaireId: number
  ) {
    const statistics = await this.responseService.getQuestionnaireStatistics(
      questionnaireId
    );
    return ApiResponseUtil.success(statistics, '获取统计信息成功');
  }

  /**
   * 获取评分系统说明
   */
  @Get('/rating-info')
  async getRatingInfo() {
    const ratingInfo = {
      school_rating: {
        system: '100分制',
        description: '学校评分采用100分制，范围为0-100分',
        grade_levels: [
          { grade: 'A+', range: '90-100分', description: '优秀' },
          { grade: 'A', range: '80-89分', description: '良好' },
          { grade: 'B', range: '70-79分', description: '中等' },
          { grade: 'C', range: '60-69分', description: '及格' },
          { grade: 'D', range: '0-59分', description: '不及格' },
        ],
      },
      teacher_rating: {
        system: '100分制',
        description: '教师评分采用100分制，范围为0-100分',
        fields: [
          'rating: 教师总体评分（必填）',
          'description: 评价描述（可选）',
        ],
      },
    };

    return ApiResponseUtil.success(ratingInfo, '获取评分系统说明成功');
  }
}
