import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 教师评价DTO
 */
export class TeacherEvaluationDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO教师ID不能为空',
      'string.max': 'SSO教师ID不能超过50个字符',
      'any.required': 'SSO教师ID是必填项',
    })
  )
  sso_teacher_id: string;

  @Rule(
    RuleType.number().integer().min(0).max(100).required().messages({
      'number.min': '评分不能小于0',
      'number.max': '评分不能大于100',
      'number.integer': '评分必须是整数',
      'any.required': '评分是必填项',
    })
  )
  rating: number;

  @Rule(
    RuleType.string().allow('').optional().max(1000).messages({
      'string.max': '评价描述不能超过1000个字符',
    })
  )
  description?: string;
}

/**
 * 提交响应DTO
 */
export class SubmitResponseDTO {
  @Rule(
    RuleType.number().integer().required().messages({
      'number.integer': '问卷ID必须是整数',
      'any.required': '问卷ID是必填项',
    })
  )
  questionnaire_id: number;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.empty': '家长手机号不能为空',
        'string.pattern.base': '家长手机号格式不正确',
        'any.required': '家长手机号是必填项',
      })
  )
  parent_phone: string;

  @Rule(
    RuleType.string().allow('').optional().max(50).messages({
      'string.max': '家长姓名不能超过50个字符',
    })
  )
  parent_name?: string;

  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学生code不能为空',
      'string.max': 'SSO学生code不能超过50个字符',
      'any.required': 'SSO学生code是必填项',
    })
  )
  sso_student_code: string;

  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学生name不能为空',
      'string.max': 'SSO学生name不能超过50个字符',
      'any.required': 'SSO学生name是必填项',
    })
  )
  sso_student_name: string;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.empty': '月份不能为空',
        'string.pattern.base': '月份格式必须为YYYY-MM',
        'any.required': '月份是必填项',
      })
  )
  month: string;

  @Rule(
    RuleType.string().allow('').optional().max(20).messages({
      'string.max': '年级编号不能超过20个字符',
    })
  )
  grade_code?: string;

  @Rule(
    RuleType.string().allow('').optional().max(20).messages({
      'string.max': '班级编号不能超过20个字符',
    })
  )
  class_code?: string;

  @Rule(
    RuleType.number().integer().min(0).max(100).optional().messages({
      'number.min': '学校评分不能小于0',
      'number.max': '学校评分不能大于100',
      'number.integer': '学校评分必须是整数',
    })
  )
  school_rating?: number;

  @Rule(
    RuleType.string().allow('').optional().max(1000).messages({
      'string.max': '学校评价描述不能超过1000个字符',
    })
  )
  school_description?: string;

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          sso_teacher_id: RuleType.string().required(),
          rating: RuleType.number().integer().min(0).max(100).required(),
          description: RuleType.string().allow('').optional(),
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': '至少需要评价一位教师',
        'any.required': '教师评价是必填项',
      })
  )
  teacher_evaluations: TeacherEvaluationDTO[];

  @Rule(
    RuleType.string().allow('').optional().max(1000).messages({
      'string.max': '备注信息不能超过1000个字符',
    })
  )
  remarks?: string;
}

/**
 * 查询响应列表DTO
 */
export class QueryResponseDTO {
  @Rule(
    RuleType.number().integer().optional().messages({
      'number.integer': '问卷ID必须是整数',
    })
  )
  questionnaire_id?: number;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.pattern.base': '家长手机号格式不正确',
      })
  )
  parent_phone?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': 'SSO学生code不能超过50个字符',
    })
  )
  sso_student_code?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string().optional().max(20).messages({
      'string.max': '年级编号不能超过20个字符',
    })
  )
  grade_code?: string;

  @Rule(
    RuleType.string().optional().max(20).messages({
      'string.max': '班级编号不能超过20个字符',
    })
  )
  class_code?: string;

  @Rule(RuleType.boolean().optional())
  is_completed?: boolean;

  @Rule(
    RuleType.number().integer().min(1).default(1).messages({
      'number.min': '页码不能小于1',
      'number.integer': '页码必须是整数',
    })
  )
  page?: number;

  @Rule(
    RuleType.number().integer().min(1).max(100).default(10).messages({
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100',
      'number.integer': '每页数量必须是整数',
    })
  )
  limit?: number;
}
